<template>
  <div class="help-layout">
    <!-- 移动端控制按钮 -->
    <div class="mobile-controls" v-if="isMobile">
      <button
        class="sidebar-toggle"
        @click="toggleSidebar"
        :class="{ 'active': sidebarVisible }"
      >
        <i class="icon-menu"></i>
        <span>菜单</span>
      </button>
      <button
        class="toc-toggle"
        @click="toggleToc"
        :class="{ 'active': tocVisible }"
      >
        <i class="icon-list"></i>
        <span>目录</span>
      </button>
    </div>

    <!-- 遮罩层 -->
    <div
      class="overlay"
      v-if="isMobile && (sidebarVisible || tocVisible)"
      @click="closeAllPanels"
    ></div>

    <!-- 左侧边栏 -->
    <aside
      ref="sidebar"
      class="sidebar"
      :class="{
        'sidebar-hidden': !sidebarVisible && isMobile,
        'sidebar-visible': sidebarVisible || !isMobile
      }"
    >
      <div class="sidebar-header" v-if="isMobile">
        <span class="sidebar-title">帮助文档</span>
        <button class="close-btn" @click="closeSidebar">
          <i class="icon-close">×</i>
        </button>
      </div>
      <div class="sidebar-menu">
        <div v-for="category in menu" :key="category.title" class="menu-category">
          <div class="category-title">{{ category.title }}</div>
          <ul class="menu-list">
            <li v-for="item in category.items" :key="item.path" class="menu-item">
              <router-link
                :to="item.path"
                class="menu-link"
                :class="{ 'menu-link-active': isMenuActive(item.path) }"
                @click="onMenuItemClick"
              >
                {{ item.name }}
              </router-link>
            </li>
          </ul>
        </div>
      </div>
    </aside>

    <!-- 主内容区 -->
    <main
      class="main-content"
      ref="mainContent"
      :class="{
        'content-expanded': (!sidebarVisible || !tocVisible) && isMobile,
        'content-full': !sidebarVisible && !tocVisible && isMobile
      }"
    >
      <HelpContent
        :doc="currentDoc"
        @content-loaded="buildToc"
        :prev-page="getPrevPage()"
        :next-page="getNextPage()"
      />
    </main>

    <!-- 右侧目录 -->
    <aside
      class="toc"
      :class="{
        'toc-hidden': !tocVisible && isMobile,
        'toc-visible': tocVisible || !isMobile
      }"
    >
      <div class="toc-header" v-if="isMobile">
        <span class="toc-title">文章导航</span>
        <button class="close-btn" @click="closeToc">
          <i class="icon-close">×</i>
        </button>
      </div>
      <div class="toc-title" v-if="!isMobile">文章导航</div>
      <ul class="toc-list">
        <li v-for="item in toc" :key="item.id" class="toc-item" :class="{ 'toc-item-h3': item.level === 3, 'active': item.id === activeTocId }">
          <a
            :href="'#' + item.id"
            class="toc-link"
            @click.prevent="scrollToAnchor(item.id)"
          >
            {{ item.text }}
          </a>
        </li>
      </ul>
    </aside>
    <!-- 悬浮窗组件 -->
    <Mider></Mider>
    <chatAi></chatAi>
  </div>
</template>

<script>
import HelpContent from './HelpContent';
import chatAi from "@/components/common/mider/chatAi";
import Mider from '@/components/common/mider/Mider';
export default {
  components: { HelpContent, chatAi , Mider },
  data() {
    return {
      menu: [
        { title: '弹性部署服务', items: [
          { name: '平台概要', path: '/help/summary' },
          { name: '快速开始', path: '/help/quick-start' },
          { name: '常见问题', path: '/help/qustion' }
        ]},
        { title: '功能介绍', items: [
          { name: '镜像仓库', path: '/help/mirror' },
          { name: 'GPU选型指南', path: '/help/gpu-selection' },
          { name: '健康检查', path: '/help/health-check' },
          { name: 'K8S YAML 导入', path: '/help/k8s-yaml-import' },
          { name: '云存储加速', path: '/help/cloud-storage' }
        ]},
        { title: '最佳实践', items: [
          { name: '弹性部署服务-Serverless 基础认识', path: '/help/deploy-serverless' },
          { name: '容器化部署 Ollama+Qwen3', path: '/help/ollama-qwen' },
          { name: '容器化部署 Ollama+Qwen3+Open WebUI', path: '/help/ollama-qwen-webui' },
          { name: '容器化部署 JupyterLab', path: '/help/jupyter-lab' },
          { name: '容器化部署 Flux.1-dev 文生图模型应用', path: '/help/flux-dev' },
          { name: '容器化部署 FramePack 图生视频框架', path: '/help/frame-pack' },
          { name: '容器化部署 Whisper', path: '/help/whisper' },
          { name: '容器化部署 StableDiffusion1.5-WebUI 应用', path: '/help/stable-diffusion1.5' },
          { name: '容器化部署 StableDiffusion2.1-WebUI 应用', path: '/help/stable-diffusion2.1' },
          { name: '容器化部署 StableDiffusion3.5-large-文生图模型应用', path: '/help/stable-diffusion3.5-large' },
          { name: '容器化部署 DailyHot', path: '/help/daily-hot' },
          { name: '容器化部署 ACE-Step', path: '/help/ace-step' },
          { name: '容器化部署 CosyVoice', path: '/help/cosy-voice' },
          { name: '容器化部署 Flux.1 Kontext Dev 图片编辑模型应用', path: '/help/Flux.1-kontext-dev' },
          { name: '容器化部署 HivisionIDPhotos', path: '/help/hivision-photos' },
          { name: '容器化部署 minicpm4', path: '/help/minicpm4' },
          { name: '容器化部署 minerU', path: '/help/mineru' },
          { name: '容器化部署 FunASR', path: '/help/funasr' },
        ]},
        { title: '账户与实名', items: [
          { name: '手机号注册与登录', path: '/help/register-login' },
          { name: '个人用户实名', path: '/help/personal-certification' }
        ]},
        { title: '服务协议', items: [
          { name: '服务协议', path: '/help/user-agreement' },
          { name: '隐私政策', path: '/help/privacy-policy' }
        ]},
        { title: '其他', items: [
          { name: 'Docker 教程', path: '/help/docker-tutorial' }
        ]}
      ],
      toc: [],
      allPages: [],
      activeTocId: null,
      isAnchorClicking: false,
      // 响应式状态
      isMobile: false,
      windowWidth: 0,
      sidebarVisible: true,
      tocVisible: true,
      // 侧边栏滚动位置保存
      sidebarScrollPosition: 0
    };
  },
  computed: {
    currentDoc() {
      return this.$route.params.doc || 'summary';
    },
    currentPath() {
      return this.$route.path;
    }
  },
  created() {
    this.flattenPages();
    this.checkScreenSize();
  },
  mounted() {
    this.updatePageTitle();

    // 从 localStorage 恢复滚动位置
    const savedScrollPosition = localStorage.getItem('helpSidebarScrollPosition');
    if (savedScrollPosition) {
      this.sidebarScrollPosition = parseInt(savedScrollPosition, 10);
    }

    this.$nextTick(() => {
      const mainContent = this.$refs.mainContent;
      if (mainContent) {
        mainContent.addEventListener('scroll', this.handleContentScroll);
      }

      const sidebar = this.$refs.sidebar;
      if (sidebar) {
        sidebar.addEventListener('scroll', this.handleSidebarScroll);
      }

      this.restoreSidebarScrollPosition();
    });

    window.addEventListener('resize', this.handleResize);
    this.checkScreenSize();
  },
  beforeDestroy() {
    const mainContent = this.$refs.mainContent;
    if (mainContent) {
      mainContent.removeEventListener('scroll', this.handleContentScroll);
    }

    const sidebar = this.$refs.sidebar;
    if (sidebar) {
      sidebar.removeEventListener('scroll', this.handleSidebarScroll);
    }

    window.removeEventListener('resize', this.handleResize);
  },
  watch: {
    '$route.path'() {
      this.updatePageTitle();
      this.$nextTick(() => {
        setTimeout(() => {
          this.restoreSidebarScrollPosition();
        }, 50);
      });
    }
  },
  methods: {
    slugify(text) {
      let slug = text
        .toLowerCase()
        .replace(/\s+/g, '-')           // 空格转为-
        .replace(/[^a-z0-9\-]+/g, '')   // 只保留小写字母、数字、短横线
        .replace(/\-\-+/g, '-')         // 多个-合并为一个
        .replace(/^-+/, '')              // 去除开头-
        .replace(/-+$/, '');             // 去除结尾-
      if (!slug || /^[0-9]+$/.test(slug)) {
        // 添加时间戳确保唯一性
        slug = 'toc-section-' + slug + '-' + Date.now();
      }
      return slug;
    },
    flattenPages() {
      this.allPages = [];
      this.menu.forEach(category => {
        category.items.forEach(item => {
          this.allPages.push({
            name: item.name,
            path: item.path,
            category: category.title
          });
        });
      });
    },
    getPrevPage() {
      const currentIndex = this.allPages.findIndex(page => page.path === this.currentPath);
      return currentIndex > 0 ? this.allPages[currentIndex - 1] : null;
    },
    getNextPage() {
      const currentIndex = this.allPages.findIndex(page => page.path === this.currentPath);
      return (currentIndex !== -1 && currentIndex < this.allPages.length - 1) ? this.allPages[currentIndex + 1] : null;
    },
    buildToc() {
      this.$nextTick(() => {
        const mainContent = this.$refs.mainContent;
        const docContent = mainContent.querySelector('.doc-content');
        if (!docContent) return;
        const headings = docContent.querySelectorAll('h2, h3');
        this.toc = Array.from(headings).map(h => {
          // 如果元素已经有ID，使用现有的ID，否则生成新的
          const existingId = h.id;
          const id = existingId || this.slugify(h.textContent);
          // 只有当元素没有ID时才设置ID
          if (!existingId) {
            h.id = id;
          }
          return {
            id: id,
            text: h.textContent,
            level: parseInt(h.tagName.substring(1))
          };
        });
        this.$nextTick(this.handleContentScroll);
      });
    },
    updatePageTitle() {
      const currentPath = this.$route.path;
      for (const category of this.menu) {
        for (const item of category.items) {
          if (item.path === currentPath) {
            this.currentPageTitle = item.name;
            return;
          }
        }
      }
    },
    scrollToAnchor(id) {
      const OFFSET = 30;
      this.isAnchorClicking = true;
      this.activeTocId = id;
      const mainContent = this.$refs.mainContent;
      const docContent = mainContent.querySelector('.doc-content');
      const element = document.getElementById(id);
      if (element) {
        const offsetTop = element.offsetTop - docContent.offsetTop;
        mainContent.scrollTop = offsetTop - OFFSET;
      }
      setTimeout(() => {
        this.isAnchorClicking = false;
      }, 100);
    },
    handleContentScroll() {
      if (this.isAnchorClicking) return;
      const OFFSET = 30;
      const mainContent = this.$refs.mainContent;
      const docContent = mainContent.querySelector('.doc-content');
      if (!docContent) return;
      const headings = docContent.querySelectorAll('h2, h3');
      let activeId = null;
      const scrollTop = mainContent.scrollTop;
      for (let i = headings.length - 1; i >= 0; i--) {
        const heading = headings[i];
        if (heading.offsetTop - OFFSET <= scrollTop) {
          activeId = heading.id;
          break;
        }
      }
      this.activeTocId = activeId;
    },
    isMenuActive(path) {
      if (this.$route.path === path) return true;
      if ((this.$route.path === '/help' || this.$route.path === '/help/') && this.menu[0].items[0].path === path) return true;
      return false;
    },

    checkScreenSize() {
      this.windowWidth = window.innerWidth;
      const wasMobile = this.isMobile;
      this.isMobile = this.windowWidth <= 992;

      // 如果从移动端切换到桌面端，显示所有面板
      if (wasMobile && !this.isMobile) {
        this.sidebarVisible = true;
        this.tocVisible = true;
      }
      // 如果从桌面端切换到移动端，隐藏侧边栏
      else if (!wasMobile && this.isMobile) {
        this.sidebarVisible = false;
        this.tocVisible = false;
      }
    },

    handleResize() {
      clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(() => {
        this.checkScreenSize();
      }, 250);
    },

    toggleSidebar() {
      this.sidebarVisible = !this.sidebarVisible;
      if (this.sidebarVisible && this.tocVisible) {
        this.tocVisible = false;
      }
    },

    toggleToc() {
      this.tocVisible = !this.tocVisible;
      if (this.tocVisible && this.sidebarVisible) {
        this.sidebarVisible = false;
      }
    },

    closeSidebar() {
      this.sidebarVisible = false;
    },

    closeToc() {
      this.tocVisible = false;
    },

    closeAllPanels() {
      this.sidebarVisible = false;
      this.tocVisible = false;
    },

    onMenuItemClick() {
      if (this.isMobile) {
        this.sidebarVisible = false;
      }
    },

    handleSidebarScroll() {
      const sidebar = this.$refs.sidebar;
      if (sidebar) {
        this.sidebarScrollPosition = sidebar.scrollTop;
        localStorage.setItem('helpSidebarScrollPosition', sidebar.scrollTop.toString());
      }
    },

    restoreSidebarScrollPosition() {
      const sidebar = this.$refs.sidebar;
      if (sidebar && this.sidebarScrollPosition >= 0) {
        requestAnimationFrame(() => {
          sidebar.scrollTop = this.sidebarScrollPosition;
        });
      }
    }
  }
};
</script>

<style scoped>
.help-layout {
  display: flex;
  min-height: calc(100vh - 60px);
  background-color: #fff;
  height: calc(100vh - 60px);
  position: relative;
}

/* 移动端控制按钮 */
.mobile-controls {
  position: fixed;
  top: 70px;
  left: 10px;
  z-index: 1001;
  display: flex;
  gap: 10px;
}

.sidebar-toggle,
.toc-toggle {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sidebar-toggle:hover,
.toc-toggle:hover {
  background: #40a9ff;
  transform: translateY(-1px);
}

.sidebar-toggle.active,
.toc-toggle.active {
  background: #096dd9;
}

.icon-menu,
.icon-list {
  font-size: 14px;
  font-weight: bold;
}

.icon-menu::before {
  content: '☰';
}

.icon-list::before {
  content: '📋';
}

/* 遮罩层 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  transition: opacity 0.3s ease;
}

/* 侧边栏样式 */
.sidebar {
  width: 300px;
  border-right: 1px solid #eee;
  background-color: #f8f8f8;
  overflow-y: auto;
  height: 100%;
  transition: transform 0.3s ease;
  z-index: 1000;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #1890ff;
}

.sidebar-menu { padding: 10px 0; }
.menu-category { margin-bottom: 10px; margin-left: 30px; border-bottom: 1px solid #e7e7e7;}
.category-title {
  font-size: 16px;
  font-weight: 900;
  padding: 8px 20px;
  color: #333;
}
.menu-list { list-style: none; padding: 0; margin: 0; }
.menu-link {
  display: block;
  padding: 3px 20px 3px 30px;
  color: #666;
  text-decoration: none;
  font-size: 15px;
  transition: all 0.3s;
}
.menu-link:hover, .router-link-active {
  color: #1890ff;
  background-color: #e6f7ff;
  border-right: 3px solid #1890ff;
}
/* 主内容区 */
.main-content {
  flex: 1;
  padding: 30px 40px;
  overflow-y: auto;
  height: 100%;
  transition: margin 0.3s ease;
}

.content-expanded {
  margin-left: 0;
  margin-right: 0;
}

.content-full {
  margin-left: 0;
  margin-right: 0;
  padding: 20px;
}

/* 目录区域 */
.toc {
  width: 300px;
  padding: 10px 20px 10px 20px;
  border-left: 1px solid #eee;
  background-color: #fff;
  position: sticky;
  top: 0;
  max-height: 100vh;
  overflow-y: auto;
  transition: transform 0.3s ease;
  z-index: 1000;
}

.toc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.toc-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.toc-list { list-style: none; padding: 0; margin: 0; }
.toc-item { margin-bottom: 0px; }
.toc-item-h3 { padding-left: 15px; }
.toc-link {
  color: #666;
  text-decoration: none;
  font-size: 14px;
  display: block;
  transition: color 0.3s;
}
.toc-link:hover { color: #1890ff; }
.toc-item.active > .toc-link {
  color: #1890ff;
  font-weight: bold;
  background: #e6f7ff;
  border-radius: 3px;
}
.menu-link-active {
  color: #1890ff;
  background-color: #e6f7ff;
  border-right: 3px solid #1890ff;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .help-layout {
    position: relative;
  }

  .mobile-controls {
    display: flex;
  }

  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
  }

  .sidebar-visible {
    transform: translateX(0);
  }

  .sidebar-hidden {
    transform: translateX(-100%);
  }

  .main-content {
    width: 100%;
    padding: 80px 20px 20px;
  }

  .toc {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(100%);
  }

  .toc-visible {
    transform: translateX(0);
  }

  .toc-hidden {
    transform: translateX(100%);
  }
}

@media (min-width: 993px) {
  .mobile-controls {
    display: none;
  }

  .overlay {
    display: none;
  }

  .sidebar-header,
  .toc-header {
    display: none;
  }

  .sidebar,
  .toc {
    position: static;
    transform: none;
  }
}

/* 平板适配 */
@media (max-width: 1200px) and (min-width: 993px) {
  .sidebar {
    width: 250px;
  }

  .toc {
    width: 250px;
  }

  .main-content {
    padding: 20px 30px;
  }
}

/* 小屏幕优化 */
@media (max-width: 576px) {
  .sidebar,
  .toc {
    width: 280px;
  }

  .main-content {
    padding: 80px 15px 15px;
  }

  .mobile-controls {
    left: 5px;
    top: 65px;
  }

  .sidebar-toggle,
  .toc-toggle {
    padding: 6px 10px;
    font-size: 11px;
  }
}
</style>
