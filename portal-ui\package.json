{"name": "portal-ui", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.3.3", "core-js": "^3.8.3", "highlight.js": "^11.11.1", "js-cookie": "^3.0.5", "jsrsasign": "^11.1.0", "markdown-it-container": "^4.0.0", "swiper": "^11.2.5", "vue": "^2.6.14", "vue-router": "^3.5.1", "vue-toastification": "^1.7.14"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "html-loader": "^5.1.0", "markdown-it": "^14.1.0", "markdown-loader": "^8.0.0", "raw-loader": "^4.0.2", "vue-markdown-loader": "^2.5.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}