<template>
	<main class="page-wrapper">
		<Header/>
			<slot></slot>
    <Mider/>
		<Footer/>
	</main>
</template>

<script>
import Header from "@/components/common/header/Header";
import Footer from "@/components/common/footer/Footer";
import Mider from "@/components/common/mider/Mider";

export default {
	name: "Layout",
	components:{Header, Footer, Mider}
}
</script>

<style scoped>
.main-content{
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}
</style>
