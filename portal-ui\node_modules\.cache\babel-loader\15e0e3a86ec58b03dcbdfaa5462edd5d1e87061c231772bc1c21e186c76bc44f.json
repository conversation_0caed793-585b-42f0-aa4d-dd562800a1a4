{"ast": null, "code": "import Vue from 'vue';\nimport VueRouter from 'vue-router';\nVue.use(VueRouter);\nconst routes = [{\n  path: '/',\n  redirect: '/index'\n}, {\n  path: '/index',\n  name: 'index',\n  component: () => import('../views/Index/IndexView.vue')\n}, {\n  path: '/product',\n  name: 'product',\n  component: () => import('../views/Product/ProductView.vue')\n},\n// {\n//     path: '/product/productId/:productId',\n//     name: 'productDetails',\n//     component: () => import(\"../views/ProductDetailsView.vue\")\n// },\n{\n  path: '/example',\n  name: 'example',\n  component: () => import('../views/ExampleView.vue')\n}, {\n  path: '/algorithmcommunity',\n  name: 'algorithmcommunity',\n  component: () => import('../views/AlgorithmCommunity.vue')\n}, {\n  path: '/news',\n  name: 'news',\n  component: () => import('../views/NewsView.vue')\n}, {\n  path: '/news/newsId/:newsId',\n  name: 'newsDetails',\n  component: () => import('../views/NewsDetailsView.vue')\n}, {\n  path: '/login',\n  name: 'login',\n  component: () => import('../views/Login/login.vue')\n}, {\n  path: '/register',\n  name: 'register',\n  component: () => import('../views/Login/register.vue')\n}, {\n  path: '/forgetpass',\n  name: 'forgetpass',\n  component: () => import('../views/Login/ForgetPassView.vue')\n}, {\n  path: '/about',\n  name: 'about',\n  component: () => import('../views/About/AboutView.vue')\n}, {\n  path: '/help',\n  redirect: '/help/summary'\n}, {\n  path: '/help/:doc?',\n  name: 'help',\n  component: () => import('../views/HelpView.vue'),\n  props: true\n}, {\n  path: '/order',\n  name: 'order',\n  component: () => import('../views/Product/OrderDetail.vue')\n}, {\n  path: '/personal',\n  name: 'personal',\n  component: () => import('../views/Personal/personal.vue')\n}, {\n  path: '/userorder',\n  name: 'userorder',\n  component: () => import('../views/Ordermange/OrderView.vue')\n}, {\n  path: '/console',\n  name: 'userorder',\n  component: () => import('../views/Console.vue')\n}\n//\n// {\n//     path: '/topup',\n//     name: 'topup',\n//     component: () => import('../views/TopupView.vue')\n// }\n];\n\nconst router = new VueRouter({\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use", "routes", "path", "redirect", "name", "component", "props", "router"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport VueRouter from 'vue-router'\r\n\r\nVue.use(VueRouter)\r\n\r\nconst routes = [\r\n    {\r\n        path:'/',\r\n        redirect:'/index'\r\n    },\r\n    {\r\n        path: '/index',\r\n        name: 'index',\r\n        component: () => import('../views/Index/IndexView.vue')\r\n    },\r\n\r\n    {\r\n        path: '/product',\r\n        name: 'product',\r\n        component: () => import('../views/Product/ProductView.vue'),\r\n    },\r\n    // {\r\n    //     path: '/product/productId/:productId',\r\n    //     name: 'productDetails',\r\n    //     component: () => import(\"../views/ProductDetailsView.vue\")\r\n    // },\r\n    {\r\n        path: '/example',\r\n        name: 'example',\r\n        component: () => import('../views/ExampleView.vue')\r\n    },\r\n    {\r\n        path: '/algorithmcommunity',\r\n        name: 'algorithmcommunity',\r\n        component: () => import('../views/AlgorithmCommunity.vue')\r\n    },\r\n    {\r\n        path: '/news',\r\n        name: 'news',\r\n        component: () => import('../views/NewsView.vue')\r\n    },\r\n    {\r\n        path: '/news/newsId/:newsId',\r\n        name: 'newsDetails',\r\n        component: () => import('../views/NewsDetailsView.vue')\r\n    },\r\n    {\r\n        path: '/login',\r\n        name: 'login',\r\n        component: () => import('../views/Login/login.vue')\r\n    },\r\n    {\r\n        path: '/register',\r\n        name: 'register',\r\n        component: () => import('../views/Login/register.vue')\r\n    },\r\n    {\r\n        path: '/forgetpass',\r\n        name: 'forgetpass',\r\n        component: () => import('../views/Login/ForgetPassView.vue')\r\n\r\n    },\r\n    {\r\n        path: '/about',\r\n        name: 'about',\r\n        component: () => import('../views/About/AboutView.vue')\r\n    },\r\n    {\r\n        path: '/help',\r\n        redirect: '/help/summary'\r\n    },\r\n    {\r\n        path: '/help/:doc?',\r\n        name: 'help',\r\n        component: () => import('../views/HelpView.vue'),\r\n        props: true\r\n    },\r\n    {\r\n        path: '/order',\r\n        name: 'order',\r\n        component: () => import('../views/Product/OrderDetail.vue')\r\n    },\r\n    {\r\n        path: '/personal',\r\n        name: 'personal',\r\n        component: () => import('../views/Personal/personal.vue')\r\n    },\r\n    {\r\n        path: '/userorder',\r\n        name: 'userorder',\r\n        component: () => import('../views/Ordermange/OrderView.vue')\r\n    },\r\n    {\r\n        path: '/console',\r\n        name: 'userorder',\r\n        component: () => import('../views/Console.vue')\r\n    }\r\n    //\r\n    // {\r\n    //     path: '/topup',\r\n    //     name: 'topup',\r\n    //     component: () => import('../views/TopupView.vue')\r\n    // }\r\n]\r\n\r\nconst router = new VueRouter({\r\n    routes\r\n})\r\n\r\nexport default router\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAElCD,GAAG,CAACE,GAAG,CAACD,SAAS,CAAC;AAElB,MAAME,MAAM,GAAG,CACX;EACIC,IAAI,EAAC,GAAG;EACRC,QAAQ,EAAC;AACb,CAAC,EACD;EACID,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B;AAC1D,CAAC,EAED;EACIH,IAAI,EAAE,UAAU;EAChBE,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC;AAC9D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;EACIH,IAAI,EAAE,UAAU;EAChBE,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B;AACtD,CAAC,EACD;EACIH,IAAI,EAAE,qBAAqB;EAC3BE,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC;AAC7D,CAAC,EACD;EACIH,IAAI,EAAE,OAAO;EACbE,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB;AACnD,CAAC,EACD;EACIH,IAAI,EAAE,sBAAsB;EAC5BE,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B;AAC1D,CAAC,EACD;EACIH,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B;AACtD,CAAC,EACD;EACIH,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B;AACzD,CAAC,EACD;EACIH,IAAI,EAAE,aAAa;EACnBE,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC;AAE/D,CAAC,EACD;EACIH,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B;AAC1D,CAAC,EACD;EACIH,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE;AACd,CAAC,EACD;EACID,IAAI,EAAE,aAAa;EACnBE,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;EAChDC,KAAK,EAAE;AACX,CAAC,EACD;EACIJ,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC;AAC9D,CAAC,EACD;EACIH,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC;AAC5D,CAAC,EACD;EACIH,IAAI,EAAE,YAAY;EAClBE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC;AAC/D,CAAC,EACD;EACIH,IAAI,EAAE,UAAU;EAChBE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACH;;AAED,MAAME,MAAM,GAAG,IAAIR,SAAS,CAAC;EACzBE;AACJ,CAAC,CAAC;AAEF,eAAeM,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}