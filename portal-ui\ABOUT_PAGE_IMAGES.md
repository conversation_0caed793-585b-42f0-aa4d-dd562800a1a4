# AboutView.vue 页面所需图片资源

## 图片文件列表

为了让AboutView.vue页面正常显示，您需要在 `public/images/about/` 目录下添加以下图片文件：

### 1. 主要展示图片
- **about-hero.jpg** - 关于我们部分的主图
  - 建议尺寸：800x600px
  - 内容：展示公司办公环境、团队合作或技术设备的专业图片

### 2. 团队图片
- **team-1.jpg** - 技术研发团队图片
  - 建议尺寸：400x300px
  - 内容：技术团队工作场景或代表性图片

- **team-2.jpg** - 运维保障团队图片
  - 建议尺寸：400x300px
  - 内容：运维团队工作场景或服务器机房图片

- **team-3.jpg** - 客户服务团队图片
  - 建议尺寸：400x300px
  - 内容：客服团队工作场景或客户沟通图片

### 3. 背景图片（可选）
- **banner-bg.jpg** - 顶部Banner背景图
  - 建议尺寸：1920x1080px
  - 内容：科技感背景、数据中心或抽象科技图案

## 图片要求

1. **格式**：建议使用 .jpg 或 .webp 格式以优化加载速度
2. **质量**：高清但文件大小适中（单个文件不超过500KB）
3. **风格**：保持统一的企业级专业风格
4. **色调**：与网站主色调（蓝色系 #59bcdb）协调

## 临时解决方案

如果暂时没有合适的图片，可以：

1. 使用占位图片服务（如 placeholder.com）
2. 从免费图库下载相关图片（如 Unsplash、Pexels）
3. 暂时注释掉图片相关代码，先查看页面布局效果

## 目录结构

```
public/
└── images/
    └── about/
        ├── about-hero.jpg
        ├── banner-bg.jpg
        ├── team-1.jpg
        ├── team-2.jpg
        └── team-3.jpg
```

## 注意事项

- 确保图片文件名与代码中的引用完全一致
- 图片路径是相对于 public 目录的
- 如果修改图片文件名，需要同步更新 AboutView.vue 中的对应路径
