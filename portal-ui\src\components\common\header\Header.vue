<template>
  <div class="header-wrapper">
    <SlideNotification v-if="showComingSoon"
                       :message="notificationMessage"
                       type="warning"
                       :duration="2000"
                       @close="showComingSoon = false" />

    <!-- 导航占位符 -->
    <div class="nav-placeholder" :style="{ height: navHeight + 'px' }"></div>

    <!-- 主导航栏 -->
    <div class="main-nav" ref="mainNav">
      <div class="container">
        <!-- 电脑端导航 -->
        <div class="nav-container desktop-nav" v-if="!isMobile">
          <!-- Logo区域 -->
          <div class="logo-area">
            <a @click="navigateTo('/')" class="logo-link">
              <img src="images/logo-tiangong.png" alt="算力租赁" loading="eager">
            </a>
          </div>

          <!-- 导航菜单 -->
          <div class="nav-menu">
            <ul class="nav-list">
              <li class="nav-item">
                <a @click="navigateTo('/')" class="nav-link" :class="{'active': isActive('/')}">首页</a>
              </li>
              <li class="nav-item dropdown">
                <a @click="navigateTo('/product')" class="nav-link" :class="{'active': isActive('/product')}">
                  算力市场<i class="iconfont icon-arrow-down"></i>
                </a>
              </li>
              <li class="nav-item dropdown">
                <a @click="triggerComingSoon" class="nav-link" :class="{'active': false}">算法社区<i class="iconfont icon-arrow-down"></i></a>
              </li>

              <!-- 私有云 -->
              <!-- <li class="nav-item">
                <a @click="triggerComingSoon" class="nav-link" :class="{'active': false}">私有云</a>
              </li> -->

              <!-- 关于我们 -->
              <li class="nav-item">
                <a @click="triggerComingSoon" class="nav-link" :class="{'active': false}">关于我们</a>
              </li>
              <!-- 帮助文档 -->
              <!-- <li class="nav-item">
                <a @click="navigateTo('/help')" class="nav-link" :class="{'active': isActive('/help')}">帮助文档</a>
              </li> -->
            </ul>
          </div>

          <!-- 用户操作区 -->
          <div class="user-actions">
            <div class="auth-buttons" v-if="!isLoggedIn">
              <a @click="navigateTo('/help')" class="btn btn-login" :class="{'active': isActive('/help')}">帮助文档</a>
              <a @click="navigateTo('/login')" class="btn btn-login" :class="{'active': isActive('/login')}">控制台</a>
              <a @click="navigateTo('/login')" class="btn btn-login" :class="{'active': isActive('/login')}">登录</a>
              <a @click="navigateTo('/register')" class="btn btn-register" :class="{'active': isActive('/register')}">立即注册</a>
            </div>
            <div class="user-profile" v-if="isLoggedIn">
              <a @click="navigateTo('/help')" class="btn btn-login" :class="{'active': isActive('/help')}">帮助文档</a>
              <a @click="handleConsoleNavigation" class="btn btn-login" :class="{'active': isActive('/console'),'disabled': isConsoleLoading }" :title="isConsoleLoading ? '控制台加载中，请稍后...' : ''">控制台</a>
              <div class="user-dropdown">
                <div class="user-avatar">
                  <div class="avatar-letter">{{ userInitial }}</div>
                </div>
                <div class="dropdown-menu">
                  <!-- 用户信息区域 -->
                  <div class="user-info-section">
                    <div class="detail-item">
                      <span class="label">用户名:</span>
                      <span class="value">{{ userName }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">手机号:</span>
                      <span class="value"><i class="copy-icon"></i> {{ userPhone }}</span>
                    </div>
                    <div class="verification-tag" @click="gotoPersonal">
                      <span class="check-icon"></span> 个人认证
                      <span class="status-text">({{isReal === 1 ? '已认证' : '未认证'}})</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">可用余额:</span>
                      <span class="value">￥{{ userBalance.toFixed(2) }}</span>
                      <div class="verification-tag recharge-btn" @click="navigateToRecharge">
                        充值
                      </div>
                    </div>
                  </div>
                  <div class="menu-options">
                    <a @click="navigateTo('/personal')" :class="{'active': isActive('/personal')}">个人中心</a>
                    <a @click="navigateTo('/userorder')" :class="{'active': isActive('/userorder')}">费用中心</a>
                  </div>

                  <!-- 退出登录按钮 -->
                  <div class="logout-button-container">
                    <button class="logout-button" @click="logout">退出登录</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 移动端导航 -->
        <div class="mobile-nav" v-else>
          <div class="mobile-nav-container">
            <!-- 左侧折叠栏按钮 -->
            <button class="hamburger-btn" @click="toggleMobileMenu">
              <span class="hamburger-line" :class="{'line-1': mobileMenuOpen}"></span>
              <span class="hamburger-line" :class="{'line-2': mobileMenuOpen}"></span>
              <span class="hamburger-line" :class="{'line-3': mobileMenuOpen}"></span>
            </button>

            <!-- 中间Logo -->
            <div class="mobile-logo-area">
              <a @click="navigateTo('/')" class="logo-link">
                <img src="images/logo_tiangong.png" alt="算力租赁">
              </a>
            </div>

            <!-- 右侧用户操作区 -->
            <div class="mobile-user-actions">
              <template v-if="!isLoggedIn">
                <!-- <a @click="navigateTo('/help')" class="mobile-console-btn">
                  帮助文档
                </a> -->
                <a @click="navigateTo('/login')" class="mobile-login-btn">
                  登录
                </a>
                <a @click="navigateTo('/register')" class="mobile-register-btn">
                  注册
                </a>
              </template>
              <template v-else>
                <a @click="navigateTo('/help')" class="mobile-console-btn">
                  帮助文档
                </a>
                <a @click="handleConsoleNavigation" class="mobile-console-btn">
                  控制台
                </a>
                <div class="mobile-user-profile" @click="toggleUserMenu">
                  <div class="mobile-user-avatar">
                    <div class="avatar-letter">{{ userInitial }}</div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- 移动端菜单 -->
          <div class="mobile-menu" :class="{'open': mobileMenuOpen}">
            <div class="mobile-menu-content">
              <!-- 导航链接 -->
              <ul class="mobile-nav-list">
                <li class="mobile-nav-item">
                  <a @click="navigateTo('/')" class="mobile-nav-link" :class="{'active': isActive('/')}">
                    <i class="iconfont icon-home"></i>首页
                  </a>
                </li>
                <li class="mobile-nav-item">
                  <a @click="navigateTo('/product')" class="mobile-nav-link" :class="{'active': isActive('/product')}">
                    <i class="iconfont icon-server"></i>算力市场
                  </a>
                </li>
                <li class="mobile-nav-item">
                  <a @click="triggerComingSoon" class="mobile-nav-link">
                    <i class="iconfont icon-community"></i>算法社区
                  </a>
                </li>
                <!-- <li class="mobile-nav-item">
                  <a @click="triggerComingSoon" class="mobile-nav-link">
                    <i class="iconfont icon-cloud"></i>私有云
                  </a>
                </li> -->
                <li class="mobile-nav-item">
                  <a @click="navigateTo('/about')" class="mobile-nav-link" :class="{'active': isActive('/about')}">
                    <i class="iconfont icon-info"></i>关于我们
                  </a>
                </li>
                <!-- <li class="mobile-nav-item">
                  <a @click="navigateTo('/help')" class="mobile-nav-link" :class="{'active': isActive('/help')}">
                    <i class="iconfont icon-docs"></i>帮助文档
                  </a>
                </li> -->

                <!-- 登录/注册/控制台入口 -->
                <template v-if="!isLoggedIn">
                  <li class="mobile-nav-item">
                  <a @click="navigateTo('/help')" class="mobile-nav-link" :class="{'active': isActive('/help')}">
                    <i class="iconfont icon-docs"></i>帮助文档
                  </a>
                </li>
                  <li class="mobile-nav-item">
                    <a @click="navigateTo('/login')" class="mobile-nav-link" :class="{'active': isActive('/login')}">
                      <i class="iconfont icon-user"></i>登录
                    </a>
                  </li>
                  <li class="mobile-nav-item">
                    <a @click="navigateTo('/register')" class="mobile-nav-link" :class="{'active': isActive('/register')}">
                      <i class="iconfont icon-edit"></i>注册
                    </a>
                  </li>
                  <li class="mobile-nav-item">
                    <a @click="navigateTo('/login')" class="mobile-nav-link" :class="{'active': isActive('/console')}">
                      <i class="iconfont icon-console"></i>控制台
                    </a>
                  </li>

                </template>
                <template v-else>
                <li class="mobile-nav-item"> 
                  <a @click="navigateTo('/help')" class="mobile-nav-link" :class="{'active': isActive('/help')}">
                    <i class="iconfont icon-docs"></i>帮助文档
                  </a>
                </li>
                  <li class="mobile-nav-item">
                    <a @click="handleConsoleNavigation" class="mobile-nav-link" :class="{'active': isActive('/console')}">
                      <i class="iconfont icon-console"></i>控制台
                    </a>
                  </li>
                  <li class="mobile-nav-item">
                    <a @click="navigateTo('/personal')" class="mobile-nav-link" :class="{'active': isActive('/personal')}">
                      <i class="iconfont icon-profile"></i>个人中心
                    </a>
                  </li>
                  <li class="mobile-nav-item">
                    <a @click="logout" class="mobile-nav-link">
                      <i class="iconfont icon-logout"></i>退出登录
                    </a>
                  </li>
                </template>
              </ul>
            </div>
          </div>

          <!-- 用户菜单 -->
          <div class="mobile-user-menu" v-if="showUserMenu">
            <div class="mobile-user-info">
              <div class="mobile-username">{{ userName }}</div>
              <div class="mobile-user-phone">{{ userPhone }}</div>
            </div>
            <div class="mobile-menu-options">
              <a @click="navigateTo('/personal')" class="mobile-menu-item" :class="{'active': isActive('/personal')}">
                <i class="iconfont icon-profile"></i>个人中心
              </a>
              <a @click="navigateTo('/userorder')" class="mobile-menu-item" :class="{'active': isActive('/userorder')}">
                <i class="iconfont icon-order"></i>费用中心
              </a>
              <a @click="navigateToRecharge" class="mobile-menu-item" :class="{'active': false}">
                <i class="iconfont icon-recharge"></i>充值
              </a>
              <a @click="logout" class="mobile-menu-item logout">
                <i class="iconfont icon-logout"></i>退出登录
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {postAnyData, getAnyData, postLogin, postJsonData} from "@/api/login";
import { removeToken, getToken } from "@/utils/auth"
import SlideNotification from './SlideNotification.vue';
import Cookies from 'js-cookie'

export default {
  name: "Header",
  components: {
    SlideNotification
  },
  data() {
    return {
      userBalance: null,
      showComingSoon: false,
      notificationMessage: "",
      isLoggedIn: false,
      userName: "null",
      userPhone: "",
      notificationCount: 2,
      currentPath: '',
      navHeight: 60,
      scrollThrottleTimer: null,
      previousActivePath: null,
      isMobile: false,
      mobileMenuOpen: false,
      showUserMenu: false,
      windowWidth: 0,
      isReal:0,
      cookieWatcher: null,
      isConsoleLoading: false,
      isConsoleReady: false,
      hasTriggeredRefresh: false,
    };
  },
  computed: {
    userInitial() {
      return this.userName && this.userName.length > 0
          ? this.userName.charAt(0).toUpperCase()
          : 'N';
    }
  },
  watch: {
    '$route'(to) {
      this.currentPath = to.path;
      if (to.path === '/product') {
        this.currentPath = '/product';
      } else if (to.path === '/about') {
        this.currentPath = '/about';
      }
    }
  },
  methods: {
    isActive(route) {
      return this.currentPath === route ||
          (route === '/' && this.currentPath === '/index') ||
          (route === '/product' && this.currentPath.startsWith('/product')) ||
          (route === '/about' && this.currentPath === '/about') ||
          (route === '/help' && this.currentPath.startsWith('/help'));
    },


    checkScreenSize() {
      this.windowWidth = window.innerWidth;
      this.isMobile = this.windowWidth <= 992;
      if (!this.isMobile) {
        this.mobileMenuOpen = false;
        this.showUserMenu = false;
      }
    },

    gotoPersonal(){
      this.closeAllMenus();
      this.$router.push({
        path: '/personal',
        query: { activeTab: 'verification' }
      });
    },

    navigateToRecharge() {
      this.closeAllMenus();
      this.$router.push({
        path: '/userorder',
        query: { activeTab: 'recharge' }
      });
    },

    handleConsoleNavigation() {
      this.closeAllMenus();

      if (this.isConsoleLoading) return;

      if (!this.isLoggedIn) {
        this.navigateTo('/login');
        return;
      }

      if (this.isReal !== 1) {
        // 未实名认证，显示弹窗
        this.notificationMessage = "请先完成实名认证，正在为您跳转到对应页面";
        this.showComingSoon = true;

        // 2秒后跳转到实名认证页面
        setTimeout(() => {
          this.navigateTo('/personal', { activeTab: 'verification' });
        }, 2000);
        return;
      }

      if (!this.isConsoleReady) {
        this.notificationMessage = '控制台初始化中，请稍候...';
        this.showComingSoon = true;
        this.isConsoleLoading = true;


        if (!this.hasTriggeredRefresh) {
          this.hasTriggeredRefresh = true;
          setTimeout(() => {
            this.getUserInfo();
            this.startCookieWatcher();
          }, 2000); // 2 秒后自动刷新
        }
        return;
      }

      this.isConsoleLoading = true;
      this.navigateTo('/console');

      this.isConsoleReady = true;
      this.isConsoleLoading = false;
    },

    handleScroll() {
      if (this.scrollThrottleTimer) return;

      this.scrollThrottleTimer = setTimeout(() => {
        this.scrollThrottleTimer = null;
      }, 50);
    },

    logout() {
      this.closeAllMenus();
      postAnyData("/logout/cilent/logout").then(res => {
        if (res.data.code === 200) {
          Object.keys(Cookies.get()).forEach(cookieName => {
            Cookies.remove(cookieName);
          });
          removeToken();
          this.isLoggedIn = false;
          this.$router.push('/login');
        }
      }).catch(err => {
      });
    },

    triggerComingSoon() {
      if (this.showComingSoon) return;
      this.notificationMessage = "我们正在努力建设中，敬请期待更多精彩内容！";
      this.showComingSoon = true;
      this.closeAllMenus();
    },

    navigateTo(path, query = {}) {
      if (this.currentPath && this.currentPath !== path) {
        this.previousActivePath = this.currentPath;

        this.$nextTick(() => {
          const navLinks = document.querySelectorAll('.nav-link, .btn-login, .mobile-nav-link');
          navLinks.forEach(link => {
            if ((link.classList.contains('active') ||
                    (path === '/login' && link.classList.contains('btn-login'))) &&
                !link.classList.contains('active-exit')) {
              link.classList.add('active-exit');

              setTimeout(() => {
                link.classList.remove('active-exit');
              }, 300);
            }
          });

          this.currentPath = path;
        });
      } else {
        this.currentPath = path;
      }

      this.closeAllMenus();

      if (this.$route.path === path && JSON.stringify(this.$route.query) === JSON.stringify(query)) {
        return;
      }

      this.$router.push({
        path: path,
        query: query
      });

      window.scrollTo({
        top: 0,
        behavior: 'instant'
      });
    },

    measureNavHeight() {
      if (this.$refs.mainNav) {
        const rect = this.$refs.mainNav.getBoundingClientRect();
        if (rect.height > 0) {
          this.navHeight = rect.height;
        }
      }
    },
    // 处理 token 过期
    handleTokenExpired() {
      // 清除登录状态
      this.isLoggedIn = false;
      removeToken();
      Object.keys(Cookies.get()).forEach(cookieName => {
        Cookies.remove(cookieName);
      });

      // 显示通知
      this.notificationMessage = "由于长时间未操作，登录状态已失效，请重新登录";
      this.showComingSoon = true;


      // 2秒后自动跳转到登录页面
      setTimeout(() => {
        this.$router.push('/login');
      }, 2000);
    },
    // 处理页面可见性变化
    handleVisibilityChange() {
      if (document.visibilityState === 'visible' && this.isLoggedIn) {
        // 页面从隐藏变为可见时，检查 token 是否仍然有效
        this.getUserInfo();
      }
    },
    getUserInfo() {
      // removeToken()
      postAnyData("/logout/cilent/getInfo").then(res => {
        if (res.data.code === 200) {
          this.userName = res.data.data.nickName || "NCloud-user";
          Cookies.set('userName', res.data.data.nickName);
          this.userPhone = res.data.data.username;
          Cookies.set('userPhone', res.data.data.username);
          this.userEmail = res.data.data.email || "<EMAIL>";
          Cookies.set('userEmail', res.data.data.email);
          this.tenantId = res.data.data.tenantId || "te-default";
          Cookies.set('tenantId', res.data.data.tenantId);
          this.userId = res.data.data.id || "ac-default";
          Cookies.set('userId', res.data.data.id);
          this.userBalance = res.data.data.balance;
          Cookies.set('userBalance', res.data.data.balance);
          this.isReal = res.data.data.isReal;
          Cookies.set('isReal', this.isReal);
          this.isLoggedIn = true;


          if (Cookies.get('publicKey-C')) {
            // console.log('rsa_pubk',publicKey-C);
            return;
          }
          postJsonData("/suanleme/login", {
            correlationId: Cookies.get('userId'),
            rsaPubk: Cookies.get('publicKey-B')
          }).then(res => {
            // console.log('rsa_pubk',res.data.data.rsa_pubk);
            Cookies.set('publicKey-C', res.data.data.rsa_pubk);
            Cookies.set('suanlemeToken', res.data.data.token);

          });
        } else {
          this.handleTokenExpired();
        }
      })
    },

    toggleMobileMenu() {
      this.mobileMenuOpen = !this.mobileMenuOpen;
      if (this.mobileMenuOpen) {
        this.showUserMenu = false;
      }
    },

    toggleUserMenu() {
      this.showUserMenu = !this.showUserMenu;
      if (this.showUserMenu) {
        this.mobileMenuOpen = false;
      }
    },

    closeAllMenus() {
      this.mobileMenuOpen = false;
      this.showUserMenu = false;
    },

    startCookieWatcher() {
      this.cookieWatcher = setInterval(() => {

        const token = this.getCookie('suanlemeToken');
        const pubKey = this.getCookie('publicKey-C');
        if (token && pubKey) {
          clearInterval(this.cookieWatcher);
          this.cookieWatcher = null;
          this.isConsoleReady = true;
          this.isConsoleLoading = false;
        }
      }, 1000); // 每秒检查一次
    },

    getCookie(name) {
      const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
      return match ? match[2] : null;
    }
  },

  created() {
    window.addEventListener('scroll', this.handleScroll, { passive: true });
    window.addEventListener('resize', this.checkScreenSize);

    // 优先从本地存储读取登录状态
    const token = getToken();
    this.isLoggedIn = !!token;

    // 如果存在token，立即从cookie中读取用户信息
    if (token) {
      this.userName = Cookies.get('userName') || "null";
      this.userPhone = Cookies.get('userPhone') || "";
      this.userBalance = parseFloat(Cookies.get('userBalance')) || 0;
      this.isReal = parseInt(Cookies.get('isReal')) || 0;

      // 然后异步验证token有效性
      this.getUserInfo();
    }

    // 添加页面可见性变化监听
    document.addEventListener('visibilitychange', this.handleVisibilityChange);

    this.currentPath = this.$route.path;
    this.checkScreenSize();
  },

  mounted() {
    this.$nextTick(() => {
      this.measureNavHeight();

      let resizeTimer;
      window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
          this.measureNavHeight();
        }, 250);
      }, { passive: true });

      this.startCookieWatcher();
    });
  },

  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('resize', this.measureNavHeight);
    window.removeEventListener('resize', this.checkScreenSize);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    clearTimeout(this.scrollThrottleTimer);
  },


};
</script>

<style scoped>
/* 原有样式保持不变 */
.header-wrapper {
  position: relative;
  width: 100%;
  max-width: 100vw;
  overflow: hidden;
}

.main-nav {
  background-image: url("../../../assets/images/index/back3.png");
  background-size: cover;
  background-position: top;
  background-repeat: no-repeat;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  width: 100%;
  z-index: 1005;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  transform: translate3d(0, 0, 0);
  will-change: transform;
  transition: background-color 0.3s ease;
  width: 100vw;
}

.nav-placeholder {
  width: 100%;
  will-change: height;
}

.container {
  width: 100%;
  max-width: 100%;
  height: 60px;
  margin: 0 auto;
  padding: 0 15px;
  box-sizing: border-box;
}

/* 电脑端导航样式 */
.nav-container {
  display: flex;
  align-items: center;
  height: 60px;
  position: relative;
}

.logo-area {
  flex: 0 0 auto;
  margin-right: -50px;
}

.logo-link {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
}


.logo-area img {
  height: 120px;
  max-width: 100%;
  display: block;
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: flex-start;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0 70px;
  padding: 0;
}

.nav-item {
  position: relative;
  margin: 0 10px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 70px;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease-in-out;
  white-space: nowrap;
  cursor: pointer;
  position: relative;
}

.nav-link.active {
  color: #ffffff;
  font-weight: 500;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: 2vh;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: calc(100% - 4vh);
  height: 2px;
  background-color: #ffffff;
  transition: transform 0.3s ease-in-out;
  transform-origin: center;
}

.nav-link:hover::after,
.nav-link.active::after {
  transform: translateX(-50%) scaleX(1);
}

.nav-link.active-exit::after {
  transform: translateX(-50%) scaleX(0);
}

.nav-link i {
  font-size: 12px;
  margin-left: 5px;
}

.user-actions {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.auth-buttons {
  display: inline-flex !important;
  align-items: center !important;
  height: 100%;
  white-space: nowrap;
}

.btn {
  display: inline-block !important;
  padding: 8px 16px !important;
  font-size: 16px !important;
  border-radius: 4px !important;
  text-decoration: none !important;
  transition: all 0.3s !important;
  margin: 0 !important;
  height: auto !important;
  line-height: normal !important;
  border: none !important;
  cursor: pointer;
}

.btn-login {
  color: #ffffff !important;
  margin-right: 10px !important;
  position: relative;
}

.btn-login::after {
  content: "";
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: calc(100% - 20px);
  height: 2px;
  background-color: #ffffff;
  transition: transform 0.3s ease-in-out;
  transform-origin: center;
}

.btn-login:hover::after,
.btn-login.active::after {
  transform: translateX(-50%) scaleX(1);
}

.btn-login.active-exit::after {
  transform: translateX(-50%) scaleX(0);
}

.btn-register {
  color: #fff !important;
  border: 1px solid #ffffff!important;
  border-radius: 0px !important;
  font-size: 15px !important;
}

.btn-register:hover {
  background-color: rgba(194, 187, 187, 0.3) !important;
}

.user-profile {
  display: flex;
  align-items: center;
}

.user-dropdown {
  position: relative;
  cursor: pointer;
  margin-right: 2vh;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-letter {
  color: white;
  font-size: 18px;
  font-weight: 500;
  text-transform: uppercase;
}

.user-dropdown .dropdown-menu {
  position: absolute;
  top: 100%;
  right: -10px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  min-width: 240px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 1010;
  padding: 0;
  overflow: hidden;
}

.user-dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-info-section {
  padding: 16px;
  position: relative;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-size: 14px;
  width: 60px;
}

.value {
  color: #333;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.verification-tag {
  display: inline-flex;
  align-items: center;
  background-color: #e6f7ff;
  color: #1890ff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.recharge-btn {
  cursor: pointer;
  margin-left: 10px;
  transition: all 0.2s;
}

.recharge-btn:hover {
  background-color: #bae7ff;
}

.check-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  background-color: #1890ff;
  margin-right: 4px;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center / contain;
}

.menu-options {
  padding: 8px 0;
}

.menu-options a {
  display: block;
  padding: 10px 16px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.2s;
  font-size: 14px;
}

.menu-options a:hover {
  background-color: #f5f5f5;
}

.logout-button-container {
  padding: 12px 16px;
  background-color: #f9fafb;
}

.logout-button {
  width: 100%;
  padding: 10px 0;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: #e0e0e0;
}

/* 移动端导航样式 */
.mobile-nav {
  position: relative;
  height: 60px;
}

.mobile-nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 15px;
  position: relative;
}

/* 汉堡菜单按钮 */
.hamburger-btn {
  background: none;
  border: none;
  width: 25px;
  height: 20px;
  position: relative;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0;
  z-index: 1001;
  flex: 0 0 auto;
}

.hamburger-line {
  display: block;
  width: 100%;
  height: 3px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.hamburger-btn .line-1 {
  transform: translateY(10px) rotate(45deg);
}

.hamburger-btn .line-2 {
  opacity: 0;
}

.hamburger-btn .line-3 {
  transform: translateY(-10px) rotate(-45deg);
}

/* 中间Logo - 调大尺寸 */
.mobile-logo-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
}

.mobile-logo-area img {
  height: 100px;
  max-width: 100%;
  object-fit: contain;
}

/* 右侧用户操作区 - 增加登录/注册按钮 */
.mobile-user-actions {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 10px;
}

.mobile-login-btn,
.mobile-register-btn,
.mobile-console-btn {
  color: #fff;
  font-size: 14px;
  text-decoration: none;
  padding: 6px 10px;
  border-radius: 4px;
  white-space: nowrap;
}

.mobile-login-btn {
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.mobile-register-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.mobile-console-btn {
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-right: 5px;
}

.mobile-user-profile {
  position: relative;
  cursor: pointer;
}

.mobile-user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: 500;
}

/* 移动端菜单 */
.mobile-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 80%;
  max-width: 320px;
  height: 100vh;
  background-color: #1a1a2e;
  z-index: 1000;
  transition: right 0.3s ease;
  overflow-y: auto;
}

.mobile-menu.open {
  right: 0;
}

.mobile-menu-content {
  padding: 70px 20px 20px;
}

.mobile-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav-item {
  margin-bottom: 5px;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  color: #fff;
  text-decoration: none;
  font-size: 16px;
  border-radius: 5px;
  transition: background-color 0.2s;
}

.mobile-nav-link i {
  margin-right: 15px;
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
}

.mobile-nav-link.active {
  background-color: rgba(255, 255, 255, 0.1);
  font-weight: 500;
}

.mobile-nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 用户菜单 */
.mobile-user-menu {
  position: fixed;
  top: 60px;
  right: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  width: 220px;
  z-index: 1011;
  padding: 15px;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.mobile-user-info {
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
}

.mobile-username {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.mobile-user-phone {
  font-size: 14px;
  color: #666;
}

.mobile-menu-options {
  margin-top: 10px;
}

.mobile-menu-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  color: #333;
  text-decoration: none;
  font-size: 15px;
  transition: color 0.2s;
}

.mobile-menu-item i {
  margin-right: 10px;
  font-size: 18px;
  color: #666;
}

.mobile-menu-item:hover {
  color: #1890ff;
}

.mobile-menu-item:hover i {
  color: #1890ff;
}

.mobile-menu-item.logout {
  color: #f5222d;
}

.mobile-menu-item.logout i {
  color: #f5222d;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .desktop-nav {
    display: none !important;
  }

  .mobile-nav {
    display: block !important;
  }
}

@media (min-width: 993px) {
  .desktop-nav {
    display: flex !important;
  }

  .mobile-nav {
    display: none !important;
  }
}
</style>
