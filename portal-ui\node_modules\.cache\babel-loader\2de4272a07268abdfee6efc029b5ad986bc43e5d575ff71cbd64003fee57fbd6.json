{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"header-wrapper\"\n  }, [_vm.showComingSoon ? _c(\"SlideNotification\", {\n    attrs: {\n      message: _vm.notificationMessage,\n      type: \"warning\",\n      duration: 2000\n    },\n    on: {\n      close: function ($event) {\n        _vm.showComingSoon = false;\n      }\n    }\n  }) : _vm._e(), _c(\"div\", {\n    staticClass: \"nav-placeholder\",\n    style: {\n      height: _vm.navHeight + \"px\"\n    }\n  }), _c(\"div\", {\n    ref: \"mainNav\",\n    staticClass: \"main-nav\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [!_vm.isMobile ? _c(\"div\", {\n    staticClass: \"nav-container desktop-nav\"\n  }, [_c(\"div\", {\n    staticClass: \"logo-area\"\n  }, [_c(\"a\", {\n    staticClass: \"logo-link\",\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/\");\n      }\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: \"images/logo-tiangong.png\",\n      alt: \"算力租赁\",\n      loading: \"eager\"\n    }\n  })])]), _c(\"div\", {\n    staticClass: \"nav-menu\"\n  }, [_c(\"ul\", {\n    staticClass: \"nav-list\"\n  }, [_c(\"li\", {\n    staticClass: \"nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"nav-link\",\n    class: {\n      active: _vm.isActive(\"/\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/\");\n      }\n    }\n  }, [_vm._v(\"首页\")])]), _c(\"li\", {\n    staticClass: \"nav-item dropdown\"\n  }, [_c(\"a\", {\n    staticClass: \"nav-link\",\n    class: {\n      active: _vm.isActive(\"/product\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/product\");\n      }\n    }\n  }, [_vm._v(\" 算力市场\"), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-down\"\n  })])]), _c(\"li\", {\n    staticClass: \"nav-item dropdown\"\n  }, [_c(\"a\", {\n    staticClass: \"nav-link\",\n    class: {\n      active: false\n    },\n    on: {\n      click: _vm.triggerComingSoon\n    }\n  }, [_vm._v(\"算法社区\"), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-down\"\n  })])]), _c(\"li\", {\n    staticClass: \"nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"nav-link\",\n    class: {\n      active: _vm.isActive(\"/about\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/about\");\n      }\n    }\n  }, [_vm._v(\"关于我们\")])])])]), _c(\"div\", {\n    staticClass: \"user-actions\"\n  }, [!_vm.isLoggedIn ? _c(\"div\", {\n    staticClass: \"auth-buttons\"\n  }, [_c(\"a\", {\n    staticClass: \"btn btn-login\",\n    class: {\n      active: _vm.isActive(\"/help\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/help\");\n      }\n    }\n  }, [_vm._v(\"帮助文档\")]), _c(\"a\", {\n    staticClass: \"btn btn-login\",\n    class: {\n      active: _vm.isActive(\"/login\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/login\");\n      }\n    }\n  }, [_vm._v(\"控制台\")]), _c(\"a\", {\n    staticClass: \"btn btn-login\",\n    class: {\n      active: _vm.isActive(\"/login\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/login\");\n      }\n    }\n  }, [_vm._v(\"登录\")]), _c(\"a\", {\n    staticClass: \"btn btn-register\",\n    class: {\n      active: _vm.isActive(\"/register\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/register\");\n      }\n    }\n  }, [_vm._v(\"立即注册\")])]) : _vm._e(), _vm.isLoggedIn ? _c(\"div\", {\n    staticClass: \"user-profile\"\n  }, [_c(\"a\", {\n    staticClass: \"btn btn-login\",\n    class: {\n      active: _vm.isActive(\"/help\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/help\");\n      }\n    }\n  }, [_vm._v(\"帮助文档\")]), _c(\"a\", {\n    staticClass: \"btn btn-login\",\n    class: {\n      active: _vm.isActive(\"/console\"),\n      disabled: _vm.isConsoleLoading\n    },\n    attrs: {\n      title: _vm.isConsoleLoading ? \"控制台加载中，请稍后...\" : \"\"\n    },\n    on: {\n      click: _vm.handleConsoleNavigation\n    }\n  }, [_vm._v(\"控制台\")]), _c(\"div\", {\n    staticClass: \"user-dropdown\"\n  }, [_c(\"div\", {\n    staticClass: \"user-avatar\"\n  }, [_c(\"div\", {\n    staticClass: \"avatar-letter\"\n  }, [_vm._v(_vm._s(_vm.userInitial))])]), _c(\"div\", {\n    staticClass: \"dropdown-menu\"\n  }, [_c(\"div\", {\n    staticClass: \"user-info-section\"\n  }, [_c(\"div\", {\n    staticClass: \"detail-item\"\n  }, [_c(\"span\", {\n    staticClass: \"label\"\n  }, [_vm._v(\"用户名:\")]), _c(\"span\", {\n    staticClass: \"value\"\n  }, [_vm._v(_vm._s(_vm.userName))])]), _c(\"div\", {\n    staticClass: \"detail-item\"\n  }, [_c(\"span\", {\n    staticClass: \"label\"\n  }, [_vm._v(\"手机号:\")]), _c(\"span\", {\n    staticClass: \"value\"\n  }, [_c(\"i\", {\n    staticClass: \"copy-icon\"\n  }), _vm._v(\" \" + _vm._s(_vm.userPhone))])]), _c(\"div\", {\n    staticClass: \"verification-tag\",\n    on: {\n      click: _vm.gotoPersonal\n    }\n  }, [_c(\"span\", {\n    staticClass: \"check-icon\"\n  }), _vm._v(\" 个人认证 \"), _c(\"span\", {\n    staticClass: \"status-text\"\n  }, [_vm._v(\"(\" + _vm._s(_vm.isReal === 1 ? \"已认证\" : \"未认证\") + \")\")])]), _c(\"div\", {\n    staticClass: \"detail-item\"\n  }, [_c(\"span\", {\n    staticClass: \"label\"\n  }, [_vm._v(\"可用余额:\")]), _c(\"span\", {\n    staticClass: \"value\"\n  }, [_vm._v(\"￥\" + _vm._s(_vm.userBalance.toFixed(2)))]), _c(\"div\", {\n    staticClass: \"verification-tag recharge-btn\",\n    on: {\n      click: _vm.navigateToRecharge\n    }\n  }, [_vm._v(\" 充值 \")])])]), _c(\"div\", {\n    staticClass: \"menu-options\"\n  }, [_c(\"a\", {\n    class: {\n      active: _vm.isActive(\"/personal\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/personal\");\n      }\n    }\n  }, [_vm._v(\"个人中心\")]), _c(\"a\", {\n    class: {\n      active: _vm.isActive(\"/userorder\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/userorder\");\n      }\n    }\n  }, [_vm._v(\"费用中心\")])]), _c(\"div\", {\n    staticClass: \"logout-button-container\"\n  }, [_c(\"button\", {\n    staticClass: \"logout-button\",\n    on: {\n      click: _vm.logout\n    }\n  }, [_vm._v(\"退出登录\")])])])])]) : _vm._e()])]) : _c(\"div\", {\n    staticClass: \"mobile-nav\"\n  }, [_c(\"div\", {\n    staticClass: \"mobile-nav-container\"\n  }, [_c(\"button\", {\n    staticClass: \"hamburger-btn\",\n    on: {\n      click: _vm.toggleMobileMenu\n    }\n  }, [_c(\"span\", {\n    staticClass: \"hamburger-line\",\n    class: {\n      \"line-1\": _vm.mobileMenuOpen\n    }\n  }), _c(\"span\", {\n    staticClass: \"hamburger-line\",\n    class: {\n      \"line-2\": _vm.mobileMenuOpen\n    }\n  }), _c(\"span\", {\n    staticClass: \"hamburger-line\",\n    class: {\n      \"line-3\": _vm.mobileMenuOpen\n    }\n  })]), _c(\"div\", {\n    staticClass: \"mobile-logo-area\"\n  }, [_c(\"a\", {\n    staticClass: \"logo-link\",\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/\");\n      }\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: \"images/logo_tiangong.png\",\n      alt: \"算力租赁\"\n    }\n  })])]), _c(\"div\", {\n    staticClass: \"mobile-user-actions\"\n  }, [!_vm.isLoggedIn ? [_c(\"a\", {\n    staticClass: \"mobile-login-btn\",\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/login\");\n      }\n    }\n  }, [_vm._v(\" 登录 \")]), _c(\"a\", {\n    staticClass: \"mobile-register-btn\",\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/register\");\n      }\n    }\n  }, [_vm._v(\" 注册 \")])] : [_c(\"a\", {\n    staticClass: \"mobile-console-btn\",\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/help\");\n      }\n    }\n  }, [_vm._v(\" 帮助文档 \")]), _c(\"a\", {\n    staticClass: \"mobile-console-btn\",\n    on: {\n      click: _vm.handleConsoleNavigation\n    }\n  }, [_vm._v(\" 控制台 \")]), _c(\"div\", {\n    staticClass: \"mobile-user-profile\",\n    on: {\n      click: _vm.toggleUserMenu\n    }\n  }, [_c(\"div\", {\n    staticClass: \"mobile-user-avatar\"\n  }, [_c(\"div\", {\n    staticClass: \"avatar-letter\"\n  }, [_vm._v(_vm._s(_vm.userInitial))])])])]], 2)]), _c(\"div\", {\n    staticClass: \"mobile-menu\",\n    class: {\n      open: _vm.mobileMenuOpen\n    }\n  }, [_c(\"div\", {\n    staticClass: \"mobile-menu-content\"\n  }, [_c(\"ul\", {\n    staticClass: \"mobile-nav-list\"\n  }, [_c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      active: _vm.isActive(\"/\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/\");\n      }\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-home\"\n  }), _vm._v(\"首页 \")])]), _c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      active: _vm.isActive(\"/product\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/product\");\n      }\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-server\"\n  }), _vm._v(\"算力市场 \")])]), _c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    on: {\n      click: _vm.triggerComingSoon\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-community\"\n  }), _vm._v(\"算法社区 \")])]), _c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      active: _vm.isActive(\"/about\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/about\");\n      }\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-info\"\n  }), _vm._v(\"关于我们 \")])]), !_vm.isLoggedIn ? [_c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      active: _vm.isActive(\"/help\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/help\");\n      }\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-docs\"\n  }), _vm._v(\"帮助文档 \")])]), _c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      active: _vm.isActive(\"/login\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/login\");\n      }\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-user\"\n  }), _vm._v(\"登录 \")])]), _c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      active: _vm.isActive(\"/register\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/register\");\n      }\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-edit\"\n  }), _vm._v(\"注册 \")])]), _c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      active: _vm.isActive(\"/console\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/login\");\n      }\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-console\"\n  }), _vm._v(\"控制台 \")])])] : [_c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      active: _vm.isActive(\"/help\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/help\");\n      }\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-docs\"\n  }), _vm._v(\"帮助文档 \")])]), _c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      active: _vm.isActive(\"/console\")\n    },\n    on: {\n      click: _vm.handleConsoleNavigation\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-console\"\n  }), _vm._v(\"控制台 \")])]), _c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      active: _vm.isActive(\"/personal\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/personal\");\n      }\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-profile\"\n  }), _vm._v(\"个人中心 \")])]), _c(\"li\", {\n    staticClass: \"mobile-nav-item\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-nav-link\",\n    on: {\n      click: _vm.logout\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-logout\"\n  }), _vm._v(\"退出登录 \")])])]], 2)])]), _vm.showUserMenu ? _c(\"div\", {\n    staticClass: \"mobile-user-menu\"\n  }, [_c(\"div\", {\n    staticClass: \"mobile-user-info\"\n  }, [_c(\"div\", {\n    staticClass: \"mobile-username\"\n  }, [_vm._v(_vm._s(_vm.userName))]), _c(\"div\", {\n    staticClass: \"mobile-user-phone\"\n  }, [_vm._v(_vm._s(_vm.userPhone))])]), _c(\"div\", {\n    staticClass: \"mobile-menu-options\"\n  }, [_c(\"a\", {\n    staticClass: \"mobile-menu-item\",\n    class: {\n      active: _vm.isActive(\"/personal\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/personal\");\n      }\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-profile\"\n  }), _vm._v(\"个人中心 \")]), _c(\"a\", {\n    staticClass: \"mobile-menu-item\",\n    class: {\n      active: _vm.isActive(\"/userorder\")\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navigateTo(\"/userorder\");\n      }\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-order\"\n  }), _vm._v(\"费用中心 \")]), _c(\"a\", {\n    staticClass: \"mobile-menu-item\",\n    class: {\n      active: false\n    },\n    on: {\n      click: _vm.navigateToRecharge\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-recharge\"\n  }), _vm._v(\"充值 \")]), _c(\"a\", {\n    staticClass: \"mobile-menu-item logout\",\n    on: {\n      click: _vm.logout\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-logout\"\n  }), _vm._v(\"退出登录 \")])])]) : _vm._e()])])])], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "showComingSoon", "attrs", "message", "notificationMessage", "type", "duration", "on", "close", "$event", "_e", "style", "height", "navHeight", "ref", "isMobile", "click", "navigateTo", "src", "alt", "loading", "class", "active", "isActive", "_v", "triggerComingSoon", "isLoggedIn", "disabled", "isConsoleLoading", "title", "handleConsoleNavigation", "_s", "userInitial", "userName", "userPhone", "gotoPersonal", "isReal", "userBalance", "toFixed", "navigateToRecharge", "logout", "toggleMobileMenu", "mobileMenuOpen", "toggleUserMenu", "open", "showUserMenu", "staticRenderFns", "_withStripped"], "sources": ["D:/code/frontend/BusinessWebisite_ui/portal-ui/src/components/common/header/Header.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"header-wrapper\" },\n    [\n      _vm.showComingSoon\n        ? _c(\"SlideNotification\", {\n            attrs: {\n              message: _vm.notificationMessage,\n              type: \"warning\",\n              duration: 2000,\n            },\n            on: {\n              close: function ($event) {\n                _vm.showComingSoon = false\n              },\n            },\n          })\n        : _vm._e(),\n      _c(\"div\", {\n        staticClass: \"nav-placeholder\",\n        style: { height: _vm.navHeight + \"px\" },\n      }),\n      _c(\"div\", { ref: \"mainNav\", staticClass: \"main-nav\" }, [\n        _c(\"div\", { staticClass: \"container\" }, [\n          !_vm.isMobile\n            ? _c(\"div\", { staticClass: \"nav-container desktop-nav\" }, [\n                _c(\"div\", { staticClass: \"logo-area\" }, [\n                  _c(\n                    \"a\",\n                    {\n                      staticClass: \"logo-link\",\n                      on: {\n                        click: function ($event) {\n                          return _vm.navigateTo(\"/\")\n                        },\n                      },\n                    },\n                    [\n                      _c(\"img\", {\n                        attrs: {\n                          src: \"images/logo-tiangong.png\",\n                          alt: \"算力租赁\",\n                          loading: \"eager\",\n                        },\n                      }),\n                    ]\n                  ),\n                ]),\n                _c(\"div\", { staticClass: \"nav-menu\" }, [\n                  _c(\"ul\", { staticClass: \"nav-list\" }, [\n                    _c(\"li\", { staticClass: \"nav-item\" }, [\n                      _c(\n                        \"a\",\n                        {\n                          staticClass: \"nav-link\",\n                          class: { active: _vm.isActive(\"/\") },\n                          on: {\n                            click: function ($event) {\n                              return _vm.navigateTo(\"/\")\n                            },\n                          },\n                        },\n                        [_vm._v(\"首页\")]\n                      ),\n                    ]),\n                    _c(\"li\", { staticClass: \"nav-item dropdown\" }, [\n                      _c(\n                        \"a\",\n                        {\n                          staticClass: \"nav-link\",\n                          class: { active: _vm.isActive(\"/product\") },\n                          on: {\n                            click: function ($event) {\n                              return _vm.navigateTo(\"/product\")\n                            },\n                          },\n                        },\n                        [\n                          _vm._v(\" 算力市场\"),\n                          _c(\"i\", { staticClass: \"iconfont icon-arrow-down\" }),\n                        ]\n                      ),\n                    ]),\n                    _c(\"li\", { staticClass: \"nav-item dropdown\" }, [\n                      _c(\n                        \"a\",\n                        {\n                          staticClass: \"nav-link\",\n                          class: { active: false },\n                          on: { click: _vm.triggerComingSoon },\n                        },\n                        [\n                          _vm._v(\"算法社区\"),\n                          _c(\"i\", { staticClass: \"iconfont icon-arrow-down\" }),\n                        ]\n                      ),\n                    ]),\n                    _c(\"li\", { staticClass: \"nav-item\" }, [\n                      _c(\n                        \"a\",\n                        {\n                          staticClass: \"nav-link\",\n                          class: { active: _vm.isActive(\"/about\") },\n                          on: {\n                            click: function ($event) {\n                              return _vm.navigateTo(\"/about\")\n                            },\n                          },\n                        },\n                        [_vm._v(\"关于我们\")]\n                      ),\n                    ]),\n                  ]),\n                ]),\n                _c(\"div\", { staticClass: \"user-actions\" }, [\n                  !_vm.isLoggedIn\n                    ? _c(\"div\", { staticClass: \"auth-buttons\" }, [\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"btn btn-login\",\n                            class: { active: _vm.isActive(\"/help\") },\n                            on: {\n                              click: function ($event) {\n                                return _vm.navigateTo(\"/help\")\n                              },\n                            },\n                          },\n                          [_vm._v(\"帮助文档\")]\n                        ),\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"btn btn-login\",\n                            class: { active: _vm.isActive(\"/login\") },\n                            on: {\n                              click: function ($event) {\n                                return _vm.navigateTo(\"/login\")\n                              },\n                            },\n                          },\n                          [_vm._v(\"控制台\")]\n                        ),\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"btn btn-login\",\n                            class: { active: _vm.isActive(\"/login\") },\n                            on: {\n                              click: function ($event) {\n                                return _vm.navigateTo(\"/login\")\n                              },\n                            },\n                          },\n                          [_vm._v(\"登录\")]\n                        ),\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"btn btn-register\",\n                            class: { active: _vm.isActive(\"/register\") },\n                            on: {\n                              click: function ($event) {\n                                return _vm.navigateTo(\"/register\")\n                              },\n                            },\n                          },\n                          [_vm._v(\"立即注册\")]\n                        ),\n                      ])\n                    : _vm._e(),\n                  _vm.isLoggedIn\n                    ? _c(\"div\", { staticClass: \"user-profile\" }, [\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"btn btn-login\",\n                            class: { active: _vm.isActive(\"/help\") },\n                            on: {\n                              click: function ($event) {\n                                return _vm.navigateTo(\"/help\")\n                              },\n                            },\n                          },\n                          [_vm._v(\"帮助文档\")]\n                        ),\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"btn btn-login\",\n                            class: {\n                              active: _vm.isActive(\"/console\"),\n                              disabled: _vm.isConsoleLoading,\n                            },\n                            attrs: {\n                              title: _vm.isConsoleLoading\n                                ? \"控制台加载中，请稍后...\"\n                                : \"\",\n                            },\n                            on: { click: _vm.handleConsoleNavigation },\n                          },\n                          [_vm._v(\"控制台\")]\n                        ),\n                        _c(\"div\", { staticClass: \"user-dropdown\" }, [\n                          _c(\"div\", { staticClass: \"user-avatar\" }, [\n                            _c(\"div\", { staticClass: \"avatar-letter\" }, [\n                              _vm._v(_vm._s(_vm.userInitial)),\n                            ]),\n                          ]),\n                          _c(\"div\", { staticClass: \"dropdown-menu\" }, [\n                            _c(\"div\", { staticClass: \"user-info-section\" }, [\n                              _c(\"div\", { staticClass: \"detail-item\" }, [\n                                _c(\"span\", { staticClass: \"label\" }, [\n                                  _vm._v(\"用户名:\"),\n                                ]),\n                                _c(\"span\", { staticClass: \"value\" }, [\n                                  _vm._v(_vm._s(_vm.userName)),\n                                ]),\n                              ]),\n                              _c(\"div\", { staticClass: \"detail-item\" }, [\n                                _c(\"span\", { staticClass: \"label\" }, [\n                                  _vm._v(\"手机号:\"),\n                                ]),\n                                _c(\"span\", { staticClass: \"value\" }, [\n                                  _c(\"i\", { staticClass: \"copy-icon\" }),\n                                  _vm._v(\" \" + _vm._s(_vm.userPhone)),\n                                ]),\n                              ]),\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"verification-tag\",\n                                  on: { click: _vm.gotoPersonal },\n                                },\n                                [\n                                  _c(\"span\", { staticClass: \"check-icon\" }),\n                                  _vm._v(\" 个人认证 \"),\n                                  _c(\"span\", { staticClass: \"status-text\" }, [\n                                    _vm._v(\n                                      \"(\" +\n                                        _vm._s(\n                                          _vm.isReal === 1 ? \"已认证\" : \"未认证\"\n                                        ) +\n                                        \")\"\n                                    ),\n                                  ]),\n                                ]\n                              ),\n                              _c(\"div\", { staticClass: \"detail-item\" }, [\n                                _c(\"span\", { staticClass: \"label\" }, [\n                                  _vm._v(\"可用余额:\"),\n                                ]),\n                                _c(\"span\", { staticClass: \"value\" }, [\n                                  _vm._v(\n                                    \"￥\" + _vm._s(_vm.userBalance.toFixed(2))\n                                  ),\n                                ]),\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass:\n                                      \"verification-tag recharge-btn\",\n                                    on: { click: _vm.navigateToRecharge },\n                                  },\n                                  [_vm._v(\" 充值 \")]\n                                ),\n                              ]),\n                            ]),\n                            _c(\"div\", { staticClass: \"menu-options\" }, [\n                              _c(\n                                \"a\",\n                                {\n                                  class: { active: _vm.isActive(\"/personal\") },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.navigateTo(\"/personal\")\n                                    },\n                                  },\n                                },\n                                [_vm._v(\"个人中心\")]\n                              ),\n                              _c(\n                                \"a\",\n                                {\n                                  class: { active: _vm.isActive(\"/userorder\") },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.navigateTo(\"/userorder\")\n                                    },\n                                  },\n                                },\n                                [_vm._v(\"费用中心\")]\n                              ),\n                            ]),\n                            _c(\n                              \"div\",\n                              { staticClass: \"logout-button-container\" },\n                              [\n                                _c(\n                                  \"button\",\n                                  {\n                                    staticClass: \"logout-button\",\n                                    on: { click: _vm.logout },\n                                  },\n                                  [_vm._v(\"退出登录\")]\n                                ),\n                              ]\n                            ),\n                          ]),\n                        ]),\n                      ])\n                    : _vm._e(),\n                ]),\n              ])\n            : _c(\"div\", { staticClass: \"mobile-nav\" }, [\n                _c(\"div\", { staticClass: \"mobile-nav-container\" }, [\n                  _c(\n                    \"button\",\n                    {\n                      staticClass: \"hamburger-btn\",\n                      on: { click: _vm.toggleMobileMenu },\n                    },\n                    [\n                      _c(\"span\", {\n                        staticClass: \"hamburger-line\",\n                        class: { \"line-1\": _vm.mobileMenuOpen },\n                      }),\n                      _c(\"span\", {\n                        staticClass: \"hamburger-line\",\n                        class: { \"line-2\": _vm.mobileMenuOpen },\n                      }),\n                      _c(\"span\", {\n                        staticClass: \"hamburger-line\",\n                        class: { \"line-3\": _vm.mobileMenuOpen },\n                      }),\n                    ]\n                  ),\n                  _c(\"div\", { staticClass: \"mobile-logo-area\" }, [\n                    _c(\n                      \"a\",\n                      {\n                        staticClass: \"logo-link\",\n                        on: {\n                          click: function ($event) {\n                            return _vm.navigateTo(\"/\")\n                          },\n                        },\n                      },\n                      [\n                        _c(\"img\", {\n                          attrs: {\n                            src: \"images/logo_tiangong.png\",\n                            alt: \"算力租赁\",\n                          },\n                        }),\n                      ]\n                    ),\n                  ]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"mobile-user-actions\" },\n                    [\n                      !_vm.isLoggedIn\n                        ? [\n                            _c(\n                              \"a\",\n                              {\n                                staticClass: \"mobile-login-btn\",\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.navigateTo(\"/login\")\n                                  },\n                                },\n                              },\n                              [_vm._v(\" 登录 \")]\n                            ),\n                            _c(\n                              \"a\",\n                              {\n                                staticClass: \"mobile-register-btn\",\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.navigateTo(\"/register\")\n                                  },\n                                },\n                              },\n                              [_vm._v(\" 注册 \")]\n                            ),\n                          ]\n                        : [\n                            _c(\n                              \"a\",\n                              {\n                                staticClass: \"mobile-console-btn\",\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.navigateTo(\"/help\")\n                                  },\n                                },\n                              },\n                              [_vm._v(\" 帮助文档 \")]\n                            ),\n                            _c(\n                              \"a\",\n                              {\n                                staticClass: \"mobile-console-btn\",\n                                on: { click: _vm.handleConsoleNavigation },\n                              },\n                              [_vm._v(\" 控制台 \")]\n                            ),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"mobile-user-profile\",\n                                on: { click: _vm.toggleUserMenu },\n                              },\n                              [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"mobile-user-avatar\" },\n                                  [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"avatar-letter\" },\n                                      [_vm._v(_vm._s(_vm.userInitial))]\n                                    ),\n                                  ]\n                                ),\n                              ]\n                            ),\n                          ],\n                    ],\n                    2\n                  ),\n                ]),\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"mobile-menu\",\n                    class: { open: _vm.mobileMenuOpen },\n                  },\n                  [\n                    _c(\"div\", { staticClass: \"mobile-menu-content\" }, [\n                      _c(\n                        \"ul\",\n                        { staticClass: \"mobile-nav-list\" },\n                        [\n                          _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                            _c(\n                              \"a\",\n                              {\n                                staticClass: \"mobile-nav-link\",\n                                class: { active: _vm.isActive(\"/\") },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.navigateTo(\"/\")\n                                  },\n                                },\n                              },\n                              [\n                                _c(\"i\", { staticClass: \"iconfont icon-home\" }),\n                                _vm._v(\"首页 \"),\n                              ]\n                            ),\n                          ]),\n                          _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                            _c(\n                              \"a\",\n                              {\n                                staticClass: \"mobile-nav-link\",\n                                class: { active: _vm.isActive(\"/product\") },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.navigateTo(\"/product\")\n                                  },\n                                },\n                              },\n                              [\n                                _c(\"i\", {\n                                  staticClass: \"iconfont icon-server\",\n                                }),\n                                _vm._v(\"算力市场 \"),\n                              ]\n                            ),\n                          ]),\n                          _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                            _c(\n                              \"a\",\n                              {\n                                staticClass: \"mobile-nav-link\",\n                                on: { click: _vm.triggerComingSoon },\n                              },\n                              [\n                                _c(\"i\", {\n                                  staticClass: \"iconfont icon-community\",\n                                }),\n                                _vm._v(\"算法社区 \"),\n                              ]\n                            ),\n                          ]),\n                          _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                            _c(\n                              \"a\",\n                              {\n                                staticClass: \"mobile-nav-link\",\n                                class: { active: _vm.isActive(\"/about\") },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.navigateTo(\"/about\")\n                                  },\n                                },\n                              },\n                              [\n                                _c(\"i\", { staticClass: \"iconfont icon-info\" }),\n                                _vm._v(\"关于我们 \"),\n                              ]\n                            ),\n                          ]),\n                          !_vm.isLoggedIn\n                            ? [\n                                _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                                  _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"mobile-nav-link\",\n                                      class: { active: _vm.isActive(\"/help\") },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.navigateTo(\"/help\")\n                                        },\n                                      },\n                                    },\n                                    [\n                                      _c(\"i\", {\n                                        staticClass: \"iconfont icon-docs\",\n                                      }),\n                                      _vm._v(\"帮助文档 \"),\n                                    ]\n                                  ),\n                                ]),\n                                _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                                  _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"mobile-nav-link\",\n                                      class: { active: _vm.isActive(\"/login\") },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.navigateTo(\"/login\")\n                                        },\n                                      },\n                                    },\n                                    [\n                                      _c(\"i\", {\n                                        staticClass: \"iconfont icon-user\",\n                                      }),\n                                      _vm._v(\"登录 \"),\n                                    ]\n                                  ),\n                                ]),\n                                _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                                  _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"mobile-nav-link\",\n                                      class: {\n                                        active: _vm.isActive(\"/register\"),\n                                      },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.navigateTo(\"/register\")\n                                        },\n                                      },\n                                    },\n                                    [\n                                      _c(\"i\", {\n                                        staticClass: \"iconfont icon-edit\",\n                                      }),\n                                      _vm._v(\"注册 \"),\n                                    ]\n                                  ),\n                                ]),\n                                _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                                  _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"mobile-nav-link\",\n                                      class: {\n                                        active: _vm.isActive(\"/console\"),\n                                      },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.navigateTo(\"/login\")\n                                        },\n                                      },\n                                    },\n                                    [\n                                      _c(\"i\", {\n                                        staticClass: \"iconfont icon-console\",\n                                      }),\n                                      _vm._v(\"控制台 \"),\n                                    ]\n                                  ),\n                                ]),\n                              ]\n                            : [\n                                _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                                  _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"mobile-nav-link\",\n                                      class: { active: _vm.isActive(\"/help\") },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.navigateTo(\"/help\")\n                                        },\n                                      },\n                                    },\n                                    [\n                                      _c(\"i\", {\n                                        staticClass: \"iconfont icon-docs\",\n                                      }),\n                                      _vm._v(\"帮助文档 \"),\n                                    ]\n                                  ),\n                                ]),\n                                _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                                  _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"mobile-nav-link\",\n                                      class: {\n                                        active: _vm.isActive(\"/console\"),\n                                      },\n                                      on: {\n                                        click: _vm.handleConsoleNavigation,\n                                      },\n                                    },\n                                    [\n                                      _c(\"i\", {\n                                        staticClass: \"iconfont icon-console\",\n                                      }),\n                                      _vm._v(\"控制台 \"),\n                                    ]\n                                  ),\n                                ]),\n                                _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                                  _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"mobile-nav-link\",\n                                      class: {\n                                        active: _vm.isActive(\"/personal\"),\n                                      },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.navigateTo(\"/personal\")\n                                        },\n                                      },\n                                    },\n                                    [\n                                      _c(\"i\", {\n                                        staticClass: \"iconfont icon-profile\",\n                                      }),\n                                      _vm._v(\"个人中心 \"),\n                                    ]\n                                  ),\n                                ]),\n                                _c(\"li\", { staticClass: \"mobile-nav-item\" }, [\n                                  _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"mobile-nav-link\",\n                                      on: { click: _vm.logout },\n                                    },\n                                    [\n                                      _c(\"i\", {\n                                        staticClass: \"iconfont icon-logout\",\n                                      }),\n                                      _vm._v(\"退出登录 \"),\n                                    ]\n                                  ),\n                                ]),\n                              ],\n                        ],\n                        2\n                      ),\n                    ]),\n                  ]\n                ),\n                _vm.showUserMenu\n                  ? _c(\"div\", { staticClass: \"mobile-user-menu\" }, [\n                      _c(\"div\", { staticClass: \"mobile-user-info\" }, [\n                        _c(\"div\", { staticClass: \"mobile-username\" }, [\n                          _vm._v(_vm._s(_vm.userName)),\n                        ]),\n                        _c(\"div\", { staticClass: \"mobile-user-phone\" }, [\n                          _vm._v(_vm._s(_vm.userPhone)),\n                        ]),\n                      ]),\n                      _c(\"div\", { staticClass: \"mobile-menu-options\" }, [\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"mobile-menu-item\",\n                            class: { active: _vm.isActive(\"/personal\") },\n                            on: {\n                              click: function ($event) {\n                                return _vm.navigateTo(\"/personal\")\n                              },\n                            },\n                          },\n                          [\n                            _c(\"i\", { staticClass: \"iconfont icon-profile\" }),\n                            _vm._v(\"个人中心 \"),\n                          ]\n                        ),\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"mobile-menu-item\",\n                            class: { active: _vm.isActive(\"/userorder\") },\n                            on: {\n                              click: function ($event) {\n                                return _vm.navigateTo(\"/userorder\")\n                              },\n                            },\n                          },\n                          [\n                            _c(\"i\", { staticClass: \"iconfont icon-order\" }),\n                            _vm._v(\"费用中心 \"),\n                          ]\n                        ),\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"mobile-menu-item\",\n                            class: { active: false },\n                            on: { click: _vm.navigateToRecharge },\n                          },\n                          [\n                            _c(\"i\", { staticClass: \"iconfont icon-recharge\" }),\n                            _vm._v(\"充值 \"),\n                          ]\n                        ),\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"mobile-menu-item logout\",\n                            on: { click: _vm.logout },\n                          },\n                          [\n                            _c(\"i\", { staticClass: \"iconfont icon-logout\" }),\n                            _vm._v(\"退出登录 \"),\n                          ]\n                        ),\n                      ]),\n                    ])\n                  : _vm._e(),\n              ]),\n        ]),\n      ]),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEH,GAAG,CAACI,cAAc,GACdH,EAAE,CAAC,mBAAmB,EAAE;IACtBI,KAAK,EAAE;MACLC,OAAO,EAAEN,GAAG,CAACO,mBAAmB;MAChCC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvBZ,GAAG,CAACI,cAAc,GAAG,KAAK;MAC5B;IACF;EACF,CAAC,CAAC,GACFJ,GAAG,CAACa,EAAE,EAAE,EACZZ,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,iBAAiB;IAC9BW,KAAK,EAAE;MAAEC,MAAM,EAAEf,GAAG,CAACgB,SAAS,GAAG;IAAK;EACxC,CAAC,CAAC,EACFf,EAAE,CAAC,KAAK,EAAE;IAAEgB,GAAG,EAAE,SAAS;IAAEd,WAAW,EAAE;EAAW,CAAC,EAAE,CACrDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtC,CAACH,GAAG,CAACkB,QAAQ,GACTjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA4B,CAAC,EAAE,CACtDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,WAAW;IACxBO,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,GAAG,CAAC;MAC5B;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,KAAK,EAAE;IACRI,KAAK,EAAE;MACLgB,GAAG,EAAE,0BAA0B;MAC/BC,GAAG,EAAE,MAAM;MACXC,OAAO,EAAE;IACX;EACF,CAAC,CAAC,CACH,CACF,CACF,CAAC,EACFtB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACpCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACpCF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,UAAU;IACvBqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,GAAG;IAAE,CAAC;IACpChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,GAAG,CAAC;MAC5B;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,IAAI,CAAC,CAAC,CACf,CACF,CAAC,EACF1B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC7CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,UAAU;IACvBqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,UAAU;IAAE,CAAC;IAC3ChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,UAAU,CAAC;MACnC;IACF;EACF,CAAC,EACD,CACEpB,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,EACf1B,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAA2B,CAAC,CAAC,CACrD,CACF,CACF,CAAC,EACFF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC7CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,UAAU;IACvBqB,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAM,CAAC;IACxBf,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAAC4B;IAAkB;EACrC,CAAC,EACD,CACE5B,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,EACd1B,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAA2B,CAAC,CAAC,CACrD,CACF,CACF,CAAC,EACFF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACpCF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,UAAU;IACvBqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,QAAQ;IAAE,CAAC;IACzChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,QAAQ,CAAC;MACjC;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,CAAC,CACH,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzC,CAACH,GAAG,CAAC6B,UAAU,GACX5B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,eAAe;IAC5BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,OAAO;IAAE,CAAC;IACxChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,OAAO,CAAC;MAChC;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,EACD1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,eAAe;IAC5BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,QAAQ;IAAE,CAAC;IACzChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,QAAQ,CAAC;MACjC;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,KAAK,CAAC,CAAC,CAChB,EACD1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,eAAe;IAC5BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,QAAQ;IAAE,CAAC;IACzChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,QAAQ,CAAC;MACjC;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,IAAI,CAAC,CAAC,CACf,EACD1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,kBAAkB;IAC/BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,WAAW;IAAE,CAAC;IAC5ChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,WAAW,CAAC;MACpC;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,CAAC,GACF3B,GAAG,CAACa,EAAE,EAAE,EACZb,GAAG,CAAC6B,UAAU,GACV5B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,eAAe;IAC5BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,OAAO;IAAE,CAAC;IACxChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,OAAO,CAAC;MAChC;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,EACD1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,eAAe;IAC5BqB,KAAK,EAAE;MACLC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,UAAU,CAAC;MAChCI,QAAQ,EAAE9B,GAAG,CAAC+B;IAChB,CAAC;IACD1B,KAAK,EAAE;MACL2B,KAAK,EAAEhC,GAAG,CAAC+B,gBAAgB,GACvB,eAAe,GACf;IACN,CAAC;IACDrB,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAACiC;IAAwB;EAC3C,CAAC,EACD,CAACjC,GAAG,CAAC2B,EAAE,CAAC,KAAK,CAAC,CAAC,CAChB,EACD1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CH,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAACkC,EAAE,CAAClC,GAAG,CAACmC,WAAW,CAAC,CAAC,CAChC,CAAC,CACH,CAAC,EACFlC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACF1B,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAACkC,EAAE,CAAClC,GAAG,CAACoC,QAAQ,CAAC,CAAC,CAC7B,CAAC,CACH,CAAC,EACFnC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACF1B,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,CAAC,EACrCH,GAAG,CAAC2B,EAAE,CAAC,GAAG,GAAG3B,GAAG,CAACkC,EAAE,CAAClC,GAAG,CAACqC,SAAS,CAAC,CAAC,CACpC,CAAC,CACH,CAAC,EACFpC,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,kBAAkB;IAC/BO,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAACsC;IAAa;EAChC,CAAC,EACD,CACErC,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,CAAC,EACzCH,GAAG,CAAC2B,EAAE,CAAC,QAAQ,CAAC,EAChB1B,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAAC2B,EAAE,CACJ,GAAG,GACD3B,GAAG,CAACkC,EAAE,CACJlC,GAAG,CAACuC,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CACjC,GACD,GAAG,CACN,CACF,CAAC,CACH,CACF,EACDtC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACF1B,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAAC2B,EAAE,CACJ,GAAG,GAAG3B,GAAG,CAACkC,EAAE,CAAClC,GAAG,CAACwC,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CACzC,CACF,CAAC,EACFxC,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT,+BAA+B;IACjCO,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAAC0C;IAAmB;EACtC,CAAC,EACD,CAAC1C,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,CAAC,CACH,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CACA,GAAG,EACH;IACEuB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,WAAW;IAAE,CAAC;IAC5ChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,WAAW,CAAC;MACpC;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,EACD1B,EAAE,CACA,GAAG,EACH;IACEuB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,YAAY;IAAE,CAAC;IAC7ChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,YAAY,CAAC;MACrC;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,CAAC,EACF1B,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAC1C,CACEF,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,eAAe;IAC5BO,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAAC2C;IAAO;EAC1B,CAAC,EACD,CAAC3C,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,CACF,CACF,CAAC,CACH,CAAC,CACH,CAAC,GACF3B,GAAG,CAACa,EAAE,EAAE,CACb,CAAC,CACH,CAAC,GACFZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,eAAe;IAC5BO,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAAC4C;IAAiB;EACpC,CAAC,EACD,CACE3C,EAAE,CAAC,MAAM,EAAE;IACTE,WAAW,EAAE,gBAAgB;IAC7BqB,KAAK,EAAE;MAAE,QAAQ,EAAExB,GAAG,CAAC6C;IAAe;EACxC,CAAC,CAAC,EACF5C,EAAE,CAAC,MAAM,EAAE;IACTE,WAAW,EAAE,gBAAgB;IAC7BqB,KAAK,EAAE;MAAE,QAAQ,EAAExB,GAAG,CAAC6C;IAAe;EACxC,CAAC,CAAC,EACF5C,EAAE,CAAC,MAAM,EAAE;IACTE,WAAW,EAAE,gBAAgB;IAC7BqB,KAAK,EAAE;MAAE,QAAQ,EAAExB,GAAG,CAAC6C;IAAe;EACxC,CAAC,CAAC,CACH,CACF,EACD5C,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,WAAW;IACxBO,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,GAAG,CAAC;MAC5B;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,KAAK,EAAE;IACRI,KAAK,EAAE;MACLgB,GAAG,EAAE,0BAA0B;MAC/BC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CACF,CACF,CAAC,EACFrB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtC,CACE,CAACH,GAAG,CAAC6B,UAAU,GACX,CACE5B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,kBAAkB;IAC/BO,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,QAAQ,CAAC;MACjC;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,EACD1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,qBAAqB;IAClCO,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,WAAW,CAAC;MACpC;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CACjB,CACF,GACD,CACE1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,oBAAoB;IACjCO,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,OAAO,CAAC;MAChC;IACF;EACF,CAAC,EACD,CAACpB,GAAG,CAAC2B,EAAE,CAAC,QAAQ,CAAC,CAAC,CACnB,EACD1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,oBAAoB;IACjCO,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAACiC;IAAwB;EAC3C,CAAC,EACD,CAACjC,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAAC,CAClB,EACD1B,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,qBAAqB;IAClCO,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAAC8C;IAAe;EAClC,CAAC,EACD,CACE7C,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CAACH,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAACkC,EAAE,CAAClC,GAAG,CAACmC,WAAW,CAAC,CAAC,CAAC,CAClC,CACF,CACF,CACF,CACF,CACF,CACN,EACD,CAAC,CACF,CACF,CAAC,EACFlC,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,aAAa;IAC1BqB,KAAK,EAAE;MAAEuB,IAAI,EAAE/C,GAAG,CAAC6C;IAAe;EACpC,CAAC,EACD,CACE5C,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDF,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAClC,CACEF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,GAAG;IAAE,CAAC;IACpChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,GAAG,CAAC;MAC5B;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,EAC9CH,GAAG,CAAC2B,EAAE,CAAC,KAAK,CAAC,CACd,CACF,CACF,CAAC,EACF1B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,UAAU;IAAE,CAAC;IAC3ChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,UAAU,CAAC;MACnC;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,EACFH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CACF,CACF,CAAC,EACF1B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BO,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAAC4B;IAAkB;EACrC,CAAC,EACD,CACE3B,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,EACFH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CACF,CACF,CAAC,EACF1B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,QAAQ;IAAE,CAAC;IACzChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,QAAQ,CAAC;MACjC;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,EAC9CH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CACF,CACF,CAAC,EACF,CAAC3B,GAAG,CAAC6B,UAAU,GACX,CACE5B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,OAAO;IAAE,CAAC;IACxChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,OAAO,CAAC;MAChC;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,EACFH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CACF,CACF,CAAC,EACF1B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,QAAQ;IAAE,CAAC;IACzChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,QAAQ,CAAC;MACjC;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,EACFH,GAAG,CAAC2B,EAAE,CAAC,KAAK,CAAC,CACd,CACF,CACF,CAAC,EACF1B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BqB,KAAK,EAAE;MACLC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,WAAW;IAClC,CAAC;IACDhB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,WAAW,CAAC;MACpC;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,EACFH,GAAG,CAAC2B,EAAE,CAAC,KAAK,CAAC,CACd,CACF,CACF,CAAC,EACF1B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BqB,KAAK,EAAE;MACLC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,UAAU;IACjC,CAAC;IACDhB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,QAAQ,CAAC;MACjC;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,EACFH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CACf,CACF,CACF,CAAC,CACH,GACD,CACE1B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,OAAO;IAAE,CAAC;IACxChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,OAAO,CAAC;MAChC;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,EACFH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CACF,CACF,CAAC,EACF1B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BqB,KAAK,EAAE;MACLC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,UAAU;IACjC,CAAC;IACDhB,EAAE,EAAE;MACFS,KAAK,EAAEnB,GAAG,CAACiC;IACb;EACF,CAAC,EACD,CACEhC,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,EACFH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CACf,CACF,CACF,CAAC,EACF1B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BqB,KAAK,EAAE;MACLC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,WAAW;IAClC,CAAC;IACDhB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,WAAW,CAAC;MACpC;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,EACFH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CACF,CACF,CAAC,EACF1B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC3CF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,iBAAiB;IAC9BO,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAAC2C;IAAO;EAC1B,CAAC,EACD,CACE1C,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,EACFH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CACF,CACF,CAAC,CACH,CACN,EACD,CAAC,CACF,CACF,CAAC,CACH,CACF,EACD3B,GAAG,CAACgD,YAAY,GACZ/C,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CH,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAACkC,EAAE,CAAClC,GAAG,CAACoC,QAAQ,CAAC,CAAC,CAC7B,CAAC,EACFnC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAACkC,EAAE,CAAClC,GAAG,CAACqC,SAAS,CAAC,CAAC,CAC9B,CAAC,CACH,CAAC,EACFpC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,kBAAkB;IAC/BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,WAAW;IAAE,CAAC;IAC5ChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,WAAW,CAAC;MACpC;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,CAAC,EACjDH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CACF,EACD1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,kBAAkB;IAC/BqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,QAAQ,CAAC,YAAY;IAAE,CAAC;IAC7ChB,EAAE,EAAE;MACFS,KAAK,EAAE,SAAAA,CAAUP,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACoB,UAAU,CAAC,YAAY,CAAC;MACrC;IACF;EACF,CAAC,EACD,CACEnB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,CAAC,EAC/CH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CACF,EACD1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,kBAAkB;IAC/BqB,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAM,CAAC;IACxBf,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAAC0C;IAAmB;EACtC,CAAC,EACD,CACEzC,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,CAAC,EAClDH,GAAG,CAAC2B,EAAE,CAAC,KAAK,CAAC,CACd,CACF,EACD1B,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,yBAAyB;IACtCO,EAAE,EAAE;MAAES,KAAK,EAAEnB,GAAG,CAAC2C;IAAO;EAC1B,CAAC,EACD,CACE1C,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,CAAC,EAChDH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CACF,CACF,CAAC,CACH,CAAC,GACF3B,GAAG,CAACa,EAAE,EAAE,CACb,CAAC,CACP,CAAC,CACH,CAAC,CACH,EACD,CAAC,CACF;AACH,CAAC;AACD,IAAIoC,eAAe,GAAG,EAAE;AACxBlD,MAAM,CAACmD,aAAa,GAAG,IAAI;AAE3B,SAASnD,MAAM,EAAEkD,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}